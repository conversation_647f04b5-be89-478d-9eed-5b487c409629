#!/usr/bin/env python3
"""
测试 vLLM API 修复效果
发送少量请求验证 max_tokens 参数是否生效
"""

import json
import asyncio
import aiohttp
import time

async def test_single_request(max_tokens=128):
    """测试单个请求"""
    url = "http://localhost:8000/v1/completions"
    
    payload = {
        "model": "/share_data/llm_weights/Meta-Llama-3-8B",
        "prompt": "Write a detailed explanation about artificial intelligence in healthcare. Please provide specific examples and detailed explanations.",
        "max_tokens": max_tokens,
        "temperature": 0.0,
        "top_p": 1.0,
        "stream": False
    }
    
    print(f"测试请求，max_tokens={max_tokens}")
    print(f"URL: {url}")
    print(f"Prompt 长度: {len(payload['prompt'])} 字符")
    
    try:
        timeout = aiohttp.ClientTimeout(total=60)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            start_time = time.time()
            async with session.post(url, json=payload) as response:
                end_time = time.time()
                
                if response.status == 200:
                    result = await response.json()
                    
                    # 提取信息
                    choice = result['choices'][0]
                    message = choice['text']
                    usage = result['usage']
                    
                    print(f"✅ 请求成功!")
                    print(f"   延迟: {end_time - start_time:.3f} 秒")
                    print(f"   输入 tokens: {usage['prompt_tokens']}")
                    print(f"   输出 tokens: {usage['completion_tokens']}")
                    print(f"   总 tokens: {usage['total_tokens']}")
                    print(f"   响应长度: {len(message)} 字符")
                    print(f"   响应预览: {message[:100]}...")
                    
                    return usage['completion_tokens']
                else:
                    error_text = await response.text()
                    print(f"❌ 请求失败: HTTP {response.status}")
                    print(f"   错误信息: {error_text}")
                    return None
                    
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

async def main():
    print("=== vLLM API 修复效果测试 ===\n")
    
    # 测试不同的 max_tokens 值
    test_cases = [16, 32, 64, 128]
    
    for max_tokens in test_cases:
        print(f"\n--- 测试 max_tokens={max_tokens} ---")
        result = await test_single_request(max_tokens)
        
        if result is not None:
            if result <= 16:
                print(f"⚠️  输出 tokens ({result}) 仍然 <= 16，可能存在问题")
            else:
                print(f"✅ 输出 tokens ({result}) > 16，修复成功！")
        
        # 等待一下再发送下一个请求
        await asyncio.sleep(1)
    
    print(f"\n=== 测试完成 ===")
    print("如果所有测试的输出 tokens 都 > 16，说明修复成功！")

if __name__ == "__main__":
    asyncio.run(main())
