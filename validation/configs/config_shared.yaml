# 共享配置文件 - config_shared.yaml
# 包含 vidur 和 vLLM 都需要的通用参数

# 模型配置
model_name: "meta-llama/Meta-Llama-3-8B"
max_model_len: 4096
dtype: "auto"
trust_remote_code: false

# 并行配置
tensor_parallel_size: 1
pipeline_parallel_size: 1

# 设备配置
device: "a100"
gpu_memory_utilization: 0.9

# KV Cache 配置
block_size: 16
kv_cache_dtype: "auto"

# 调度配置
max_num_seqs: 128
max_num_batched_tokens: 4096
batch_size_cap: 128

# 随机种子
seed: 42

# 请求生成配置（用于测试）
num_requests: 512
qps: 6.45

# 长度配置
max_tokens: 2048
prefill_tokens: 2048
decode_tokens: 512

# 注意：trace_file 不是共享参数，因为 vidur 和 vLLM 使用不同格式的数据文件

# 日志配置
log_level: "info"
